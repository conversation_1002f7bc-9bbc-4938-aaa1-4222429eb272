import express from "express";
import { createServer } from "http";
import { Server } from "socket.io";
import { setupOrderSocket } from "./sockets/orderSocket.js";
import bodyParser from "body-parser";
import { config } from "dotenv";
import connectDB from "./config/database.js";
import { setupSubscriptionCronJobs } from "./utils/subscriptionCron.js";
import { setupWebhookRetryCronJob } from "./utils/webhookRetryCron.js";
import { setupPaymentMonitoringCronJob } from "./utils/paymentMonitoringCron.js";
import adminRoutes from "./routes/admin-routes.js";
import authRoutes from "./routes/public-routes.js";
import userRoutes from "./routes/user-routes.js";
import superAdminRoutes from "./routes/super-admin-routes.js";
import paymentRoutes from "./routes/payment-routes.js";
import fundTransferRoutes from "./routes/fund-transfer-routes.js";
import subscriptionRoutes from "./routes/subscription-routes.js";
import notificationRoutes from "./routes/notification-routes.js";
import marketingRoutes from "./routes/marketing-routes.js";
import optimizationRoutes from "./routes/optimization-routes.js";
import cartRoutes from "./routes/cart-routes.js";
import cors from "cors";
import passport from "passport";
import session from "express-session";

config();
connectDB();

const app = express();
const httpServer = createServer(app);

// Define allowed origins
const allowedOrigins = [
  "http://localhost:3000",
  "https://butler-web.vercel.app",
  "https://butler-web-git-main-vaidikchouhans-projects.vercel.app",
  "https://butler-web-vaidikchouhans-projects.vercel.app",
  // Add any other allowed origins
];

const io = new Server(httpServer, {
  cors: {
    origin: allowedOrigins,
    methods: ["GET", "POST"],
    credentials: true,
    allowedHeaders: ["Authorization", "Content-Type"],
    exposedHeaders: ["Authorization", "Content-Type"],
  },
});

// Configure Express CORS
app.use(
  cors({
    origin: function (origin, callback) {
      // Allow requests with no origin (like mobile apps, curl requests)
      if (!origin) return callback(null, true);

      if (allowedOrigins.indexOf(origin) === -1) {
        console.log("Allowing CORS for origin:", origin);
      }

      // Allow all origins for now to debug CORS issues
      return callback(null, true);
    },
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Authorization", "Content-Type"],
    exposedHeaders: ["Authorization", "Content-Type"],
  })
);

// Make io globally available
global.io = io;

// Setup socket handlers
setupOrderSocket(io);

app.use(bodyParser.json());
// app.use(cors("*"));
app.use(bodyParser.urlencoded({ extended: true }));

// Session configuration for passport
app.use(
  session({
    secret: process.env.JWT_SECRET || "fallback-secret",
    resave: false,
    saveUninitialized: false,
    cookie: { secure: false }, // Set to true in production with HTTPS
  })
);

// Initialize passport
app.use(passport.initialize());
app.use(passport.session());

app.get("/", (req, res) => {
  res.send("Welcome to Butler, How may I serve you?");
});

app.use("/api/v1", authRoutes);
app.use(
  "/api/v1",
  adminRoutes,
  superAdminRoutes,
  userRoutes,
  paymentRoutes,
  fundTransferRoutes,
  subscriptionRoutes,
  notificationRoutes,
  marketingRoutes,
  optimizationRoutes,
  cartRoutes
);

httpServer.listen(3001, () => {
  console.log("Server running on port 3001!");

  // Setup cron jobs
  setupSubscriptionCronJobs();
  setupWebhookRetryCronJob();
  setupPaymentMonitoringCronJob();
  console.log("Cron jobs initialized");
});
