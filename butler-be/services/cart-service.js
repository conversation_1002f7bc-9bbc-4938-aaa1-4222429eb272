import CartOperation from "../models/CartOperation.js";
import Dish from "../models/Dish.js";
import { emitCartUpdate, emitCartOperation } from "../sockets/cartSocket.js";
import Cart from "../models/Cart.js";

/**
 * Detect cart operation intent from user message
 * @param {string} message - User message
 * @returns {Object|null} - Detected operation or null if no operation detected
 */
export const detectCartOperation = (message) => {
  // Convert message to lowercase for case-insensitive matching
  const lowerMessage = message.toLowerCase();

  // Patterns for detecting add operations
  const addPatterns = [
    /add\s+(\d+)\s+(.*?)\s+to\s+(?:my\s+)?cart/i,
    /add\s+(.*?)\s+to\s+(?:my\s+)?cart/i,
    /put\s+(.*?)\s+in\s+(?:my\s+)?cart/i,
    /i\s+want\s+to\s+order\s+(.*)/i,
    /i\s+would\s+like\s+to\s+order\s+(.*)/i,
    /i\s+want\s+(.*)/i,
    /i\s+would\s+like\s+(.*)/i,
    /can\s+i\s+get\s+(.*)/i,
    /can\s+i\s+have\s+(.*)/i,
    /please\s+add\s+(.*?)\s+to\s+(?:my\s+)?cart/i,
    /give\s+me\s+(.*)/i,
    /i'll\s+take\s+(.*)/i,
    /i\s+will\s+take\s+(.*)/i,
    /i\s+need\s+(.*)/i,
    /add\s+a\s+(.*)/i,
    /add\s+an\s+(.*)/i,
    /add\s+one\s+(.*)/i,
    /add\s+(\d+)\s+(.*)/i,
  ];

  // Patterns for detecting remove operations
  const removePatterns = [
    /remove\s+(.*?)\s+from\s+(?:my\s+)?cart/i,
    /delete\s+(.*?)\s+from\s+(?:my\s+)?cart/i,
    /take\s+(.*?)\s+out\s+of\s+(?:my\s+)?cart/i,
    /i\s+don't\s+want\s+(.*?)\s+(?:anymore|any more)/i,
    /cancel\s+(.*?)\s+from\s+(?:my\s+)?order/i,
    /please\s+remove\s+(.*?)\s+from\s+(?:my\s+)?cart/i,
    /remove\s+the\s+(.*)/i,
    /delete\s+the\s+(.*)/i,
    /cancel\s+the\s+(.*)/i,
    /take\s+out\s+the\s+(.*)/i,
    /i\s+changed\s+my\s+mind\s+about\s+the\s+(.*)/i,
    /no\s+more\s+(.*)/i,
  ];

  // Patterns for clearing cart
  const clearPatterns = [
    /clear\s+(?:my\s+)?cart/i,
    /empty\s+(?:my\s+)?cart/i,
    /remove\s+everything\s+from\s+(?:my\s+)?cart/i,
    /start\s+over\s+(?:with\s+)?(?:my\s+)?cart/i,
    /reset\s+(?:my\s+)?cart/i,
    /cancel\s+(?:my\s+)?order/i,
    /cancel\s+(?:my\s+)?cart/i,
    /delete\s+(?:my\s+)?cart/i,
    /delete\s+everything/i,
    /remove\s+all\s+items/i,
    /i\s+want\s+to\s+start\s+over/i,
    /i\s+changed\s+my\s+mind\s+about\s+everything/i,
  ];

  // Check for clear cart intent
  for (const pattern of clearPatterns) {
    if (pattern.test(lowerMessage)) {
      return {
        operation: "clear",
        item: null,
        quantity: 0,
      };
    }
  }

  // Check for add intent
  for (const pattern of addPatterns) {
    const match = lowerMessage.match(pattern);
    if (match) {
      // Check if the first capture group is a number (quantity)
      if (match.length > 1) {
        const firstGroup = match[1].trim();
        if (/^\d+$/.test(firstGroup)) {
          return {
            operation: "add",
            item: match[2].trim(),
            quantity: parseInt(firstGroup, 10),
          };
        } else {
          return {
            operation: "add",
            item: firstGroup,
            quantity: 1,
          };
        }
      }
    }
  }

  // Check for remove intent
  for (const pattern of removePatterns) {
    const match = lowerMessage.match(pattern);
    if (match && match.length > 1) {
      return {
        operation: "remove",
        item: match[1].trim(),
        quantity: 1,
      };
    }
  }

  // No cart operation detected
  return null;
};

/**
 * Find dish by name or description
 * @param {string} itemName - Item name to search for
 * @param {string} foodChainId - Food chain ID
 * @param {string} outletId - Outlet ID
 * @returns {Promise<Object|null>} - Found dish or null
 */
export const findDishByName = async (itemName, foodChainId, outletId) => {
  try {
    // Clean up the item name - remove common words that might interfere with search
    const cleanedItemName = itemName
      .replace(
        /a |an |the |some |few |one |two |three |four |five |six |seven |eight |nine |ten /gi,
        ""
      )
      .replace(
        /please|thank you|thanks|kindly|would like|want|need|get|have/gi,
        ""
      )
      .trim();

    if (!cleanedItemName) {
      return null;
    }

    // Create a regex for case-insensitive partial matching
    const searchRegex = new RegExp(cleanedItemName, "i");

    // Search for dishes that match the name or description
    const dishes = await Dish.find({
      foodChain: foodChainId,
      outlets: outletId,
      isAvailable: true,
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { tags: searchRegex },
        { cuisine: searchRegex },
      ],
    }).populate("category", "name");

    if (dishes.length === 0) {
      // Try a more flexible search by splitting the item name into words
      // and searching for dishes that match any of the words
      const words = cleanedItemName
        .split(/\s+/)
        .filter((word) => word.length > 2);

      if (words.length > 0) {
        const wordQueries = words.map((word) => ({
          $or: [
            { name: new RegExp(word, "i") },
            { description: new RegExp(word, "i") },
            { tags: new RegExp(word, "i") },
            { cuisine: new RegExp(word, "i") },
          ],
        }));

        const moreFlexibleDishes = await Dish.find({
          foodChain: foodChainId,
          outlets: outletId,
          isAvailable: true,
          $or: wordQueries,
        }).populate("category", "name");

        if (moreFlexibleDishes.length > 0) {
          // Sort by relevance - dishes that match more words are more relevant
          const scoredDishes = moreFlexibleDishes.map((dish) => {
            const dishText = `${dish.name} ${dish.description || ""} ${
              dish.category?.name || ""
            } ${dish.tags?.join(" ") || ""} ${
              dish.cuisine || ""
            }`.toLowerCase();
            let score = 0;

            words.forEach((word) => {
              if (dishText.includes(word.toLowerCase())) {
                score++;
              }
            });

            return { dish, score };
          });

          // Sort by score (highest first)
          scoredDishes.sort((a, b) => b.score - a.score);

          return scoredDishes[0].dish;
        }
      }

      return null;
    }

    // Return the first match (most relevant)
    return dishes[0];
  } catch (error) {
    console.error("Error finding dish by name:", error);
    throw error;
  }
};

/**
 * Record a cart operation
 * @param {Object} operationData - Operation data
 * @returns {Promise<Object>} - Created operation
 */
export const recordCartOperation = async (operationData) => {
  try {
    const cartOperation = new CartOperation(operationData);
    await cartOperation.save();
    return cartOperation;
  } catch (error) {
    console.error("Error recording cart operation:", error);
    throw error;
  }
};

/**
 * Process a cart operation
 * @param {Object} operation - Operation details
 * @param {string} userId - User ID
 * @param {string} conversationId - Conversation ID
 * @param {string} foodChainId - Food chain ID
 * @param {string} outletId - Outlet ID
 * @returns {Promise<Object>} - Result of the operation
 */
export const processCartOperation = async (
  operation,
  userId,
  conversationId,
  foodChainId,
  outletId
) => {
  try {
    if (!operation) {
      return null;
    }

    let result = {
      success: false,
      message: "",
      operation: operation.operation,
      dish: null,
    };

    // Handle clear cart operation
    if (operation.operation === "clear") {
      // Get user's cart and clear it
      let cart = await Cart.findActiveCart(userId, foodChainId, outletId);
      if (cart) {
        await cart.clearCart();
      }

      await recordCartOperation({
        userId,
        conversationId,
        operation: "clear",
        status: "completed",
      });

      result.success = true;
      result.message =
        "I've cleared your cart. Would you like to start a new order?";
      return result;
    }

    // Find the dish by name for add/remove operations
    const dish = await findDishByName(operation.item, foodChainId, outletId);

    if (!dish) {
      await recordCartOperation({
        userId,
        conversationId,
        operation: operation.operation,
        status: "failed",
        metadata: { itemName: operation.item, reason: "Dish not found" },
      });

      // Provide a more helpful message
      if (operation.operation === "add") {
        result.message = `I couldn't find "${operation.item}" in our menu. Could you please try a different dish or check our menu for available options?`;
      } else {
        result.message = `I couldn't find "${operation.item}" in your cart or our menu. Please check the name and try again.`;
      }
      return result;
    }

    // Get or create user's cart
    let cart = await Cart.findActiveCart(userId, foodChainId, outletId);
    if (!cart) {
      cart = await Cart.createOrUpdateCart(userId, foodChainId, outletId);
    }

    // Actually perform the cart operation
    if (operation.operation === "add") {
      // Add item to cart
      await cart.addItem(
        {
          ...dish.toObject(),
          categoryName: dish.category?.name,
        },
        operation.quantity || 1
      );

      // Apply offers
      await validateAndApplyOffersToCart(cart);

      result.message = `I've added "${dish.name}" to your cart. Would you like anything else?`;
    } else if (operation.operation === "remove") {
      // Check if item is in cart
      const itemExists = cart.items.some(
        (item) => item.dishId.toString() === dish._id.toString()
      );

      if (itemExists) {
        // Remove item from cart
        await cart.removeItem(dish._id);

        // Re-apply offers
        await validateAndApplyOffersToCart(cart);

        result.message = `I've removed "${dish.name}" from your cart. Is there anything else you'd like?`;
      } else {
        result.message = `I couldn't find "${dish.name}" in your cart. Is there something else you'd like to order?`;
      }
    }

    // Record the operation
    await recordCartOperation({
      userId,
      conversationId,
      operation: operation.operation,
      dishId: dish._id,
      quantity: operation.quantity,
      status: "completed",
      metadata: { dishName: dish.name },
    });

    result.success = true;
    result.dish = dish;
    result.operation = operation.operation;
    result.quantity = operation.quantity;

    // Actually perform the cart operation
    try {
      // Find or create cart
      let cart = await Cart.findOne({
        userId,
        foodChainId,
        outletId,
      });

      if (!cart) {
        cart = new Cart({
          userId,
          foodChainId,
          outletId,
          items: [],
          appliedOffers: [],
          appliedCoupons: [],
        });
      }

      if (operation.operation === "add") {
        // Add item to cart
        await cart.addItem(dish, operation.quantity);
        result.message = `Great! I've added ${operation.quantity} ${dish.name}${
          operation.quantity > 1 ? "s" : ""
        } to your cart. Your cart now has ${cart.getItemCount()} item${
          cart.getItemCount() !== 1 ? "s" : ""
        }. Would you like to add anything else?`;
        result.cartUpdated = true;
        result.cartItemCount = cart.getItemCount();

        // Emit real-time cart update
        await emitCartUpdate(userId, foodChainId, outletId, "add");
        await emitCartOperation(userId, foodChainId, outletId, result);
      } else if (operation.operation === "remove") {
        // Check if item is in cart first
        if (cart.hasItem(dish._id)) {
          const currentQuantity = cart.getItemQuantity(dish._id);
          const quantityToRemove = operation.quantity || currentQuantity;

          if (quantityToRemove >= currentQuantity) {
            // Remove entire item if quantity to remove is >= current quantity
            await cart.removeItem(dish._id);
            result.message = `I've removed ${
              dish.name
            } from your cart. Your cart now has ${cart.getItemCount()} item${
              cart.getItemCount() !== 1 ? "s" : ""
            }. Is there anything else you'd like to do?`;
          } else {
            // Reduce quantity by the specified amount
            const newQuantity = currentQuantity - quantityToRemove;
            await cart.updateItemQuantity(dish._id, newQuantity);
            result.message = `I've removed ${quantityToRemove} ${dish.name}${
              quantityToRemove > 1 ? "s" : ""
            } from your cart. You now have ${newQuantity} ${dish.name}${
              newQuantity > 1 ? "s" : ""
            } remaining. Your cart now has ${cart.getItemCount()} item${
              cart.getItemCount() !== 1 ? "s" : ""
            }. Is there anything else you'd like to do?`;
          }
          result.cartUpdated = true;
          result.cartItemCount = cart.getItemCount();

          // Emit real-time cart update
          await emitCartUpdate(userId, foodChainId, outletId, "remove");
          await emitCartOperation(userId, foodChainId, outletId, result);
        } else {
          result.message = `${dish.name} is not in your cart. Would you like to add it instead?`;
          result.cartUpdated = false;
        }
      }
    } catch (cartError) {
      console.error("Error performing cart operation:", cartError);
      // Fallback to original behavior if cart operation fails
      if (operation.operation === "add") {
        result.message = `I found "${dish.name}" in our menu. You can add it to your cart using the + button. Would you like anything else?`;
      } else if (operation.operation === "remove") {
        result.message = `I found "${dish.name}" in our menu. If it's in your cart, you can remove it using the - button. Is there anything else you'd like to know?`;
      }
    }

    return result;
  } catch (error) {
    console.error("Error processing cart operation:", error);
    throw error;
  }
};
