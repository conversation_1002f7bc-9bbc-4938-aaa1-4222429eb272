import Offer from "../models/Offer.js";
import Order from "../models/Order.js";
import User from "../models/User.js";
import Dish from "../models/Dish.js";

/**
 * Comprehensive offer validation and application service
 */

// Main function to validate and apply offers to an order
export const validateAndApplyOffers = async (orderData, userId = null) => {
  try {
    const { outletId, items, totalAmount, foodChainId } = orderData;

    // Get all applicable offers
    const applicableOffers = await getApplicableOffersForOrder({
      outletId,
      orderAmount: totalAmount,
      customerId: userId,
      items,
      foodChainId,
    });

    // Sort offers by priority and auto-apply eligible ones
    const autoApplyOffers = applicableOffers
      .filter((offer) => offer.autoApply && offer.canAutoApply)
      .sort(
        (a, b) =>
          (b.stackingRules?.priority || 0) - (a.stackingRules?.priority || 0)
      );

    let appliedOffers = [];
    let totalDiscount = 0;
    let modifiedItems = [...items];

    // Apply auto-applicable offers
    for (const offer of autoApplyOffers) {
      const applicationResult = await applyOfferToOrder(offer, {
        items: modifiedItems,
        totalAmount: totalAmount - totalDiscount,
        outletId,
        customerId: userId,
      });

      if (applicationResult.success) {
        appliedOffers.push({
          offerId: offer._id,
          offerName: offer.name,
          offerType: offer.offerType,
          discount: applicationResult.discount,
          freeItems: applicationResult.freeItems || [],
        });

        totalDiscount += applicationResult.discount;

        // Update items if free items were added
        if (
          applicationResult.freeItems &&
          applicationResult.freeItems.length > 0
        ) {
          modifiedItems = [...modifiedItems, ...applicationResult.freeItems];
        }

        // Update offer usage analytics
        await updateOfferUsage(offer._id, applicationResult.discount, userId);
      }
    }

    return {
      success: true,
      appliedOffers,
      totalDiscount,
      finalAmount: Math.max(0, totalAmount - totalDiscount),
      modifiedItems,
      availableOffers: applicableOffers.filter((offer) => !offer.autoApply),
    };
  } catch (error) {
    console.error("Error in validateAndApplyOffers:", error);
    return {
      success: false,
      error: error.message,
      appliedOffers: [],
      totalDiscount: 0,
      finalAmount: orderData.totalAmount,
      modifiedItems: orderData.items,
    };
  }
};

// Get all applicable offers for an order
export const getApplicableOffersForOrder = async (orderData) => {
  const { outletId, orderAmount, customerId, items, foodChainId } = orderData;

  // Get all active offers for the food chain and outlet
  const now = new Date();
  const offers = await Offer.find({
    foodChainId,
    isActive: true,
    startDate: { $lte: now },
    endDate: { $gte: now },
    $or: [
      { applicableOutlets: { $in: [outletId] } },
      { applicableOutlets: { $size: 0 } }, // Offers applicable to all outlets
    ],
  })
    .populate("discountDetails.freeItemId", "name price")
    .populate("discountDetails.comboItems.dishId", "name price")
    .populate("discountDetails.requiredCategories", "name")
    .sort({ "stackingRules.priority": -1 });

  // Filter offers based on applicability
  const applicableOffers = [];

  for (const offer of offers) {
    const applicabilityResult = await checkDetailedOfferApplicability(
      offer,
      orderData
    );

    if (applicabilityResult.isApplicable) {
      applicableOffers.push({
        ...offer.toObject(),
        estimatedSavings: applicabilityResult.estimatedSavings,
        canAutoApply: applicabilityResult.canAutoApply,
        applicabilityReason: applicabilityResult.reason,
      });
    }
  }

  return applicableOffers;
};

// Detailed offer applicability check
export const checkDetailedOfferApplicability = async (offer, orderData) => {
  const { orderAmount, customerId, items, outletId } = orderData;

  try {
    // Check usage limits
    if (
      offer.usageRules?.usageLimit > 0 &&
      offer.usageRules?.usedCount >= offer.usageRules?.usageLimit
    ) {
      return { isApplicable: false, reason: "Usage limit reached" };
    }

    // Check time restrictions
    if (offer.discountDetails?.timeRestrictions) {
      const timeCheck = checkTimeRestrictions(
        offer.discountDetails.timeRestrictions
      );
      if (!timeCheck.isValid) {
        return { isApplicable: false, reason: timeCheck.reason };
      }
    }

    // Check minimum order value
    if (
      offer.discountDetails?.minimumOrderValue &&
      orderAmount < offer.discountDetails.minimumOrderValue
    ) {
      return {
        isApplicable: false,
        reason: `Minimum order value of ₹${offer.discountDetails.minimumOrderValue} not met`,
      };
    }

    // Check customer-specific restrictions
    if (customerId) {
      const customerCheck = await checkCustomerEligibility(offer, customerId);
      if (!customerCheck.isEligible) {
        return { isApplicable: false, reason: customerCheck.reason };
      }
    }

    // Check offer-type specific requirements
    const typeSpecificCheck = await checkOfferTypeRequirements(
      offer,
      orderData
    );
    if (!typeSpecificCheck.isValid) {
      return { isApplicable: false, reason: typeSpecificCheck.reason };
    }

    // Calculate estimated savings
    const estimatedSavings = await calculateEstimatedSavings(offer, orderData);

    return {
      isApplicable: true,
      estimatedSavings,
      canAutoApply: true,
      reason: "Offer is applicable",
    };
  } catch (error) {
    console.error("Error checking offer applicability:", error);
    return { isApplicable: false, reason: "Error validating offer" };
  }
};

// Check time restrictions
const checkTimeRestrictions = (timeRestrictions) => {
  const now = new Date();
  const currentDay = now.getDay();
  const currentTime =
    now.getHours().toString().padStart(2, "0") +
    ":" +
    now.getMinutes().toString().padStart(2, "0");

  const { startTime, endTime, daysOfWeek } = timeRestrictions;

  // Check day of week restriction
  if (daysOfWeek && daysOfWeek.length > 0 && !daysOfWeek.includes(currentDay)) {
    return { isValid: false, reason: "Not available on this day of the week" };
  }

  // Check time restriction
  if (startTime && endTime) {
    if (currentTime < startTime || currentTime > endTime) {
      return {
        isValid: false,
        reason: `Only available between ${startTime} and ${endTime}`,
      };
    }
  }

  return { isValid: true };
};

// Check customer eligibility
const checkCustomerEligibility = async (offer, customerId) => {
  try {
    // Check customer tier restrictions
    if (
      offer.discountDetails?.customerTiers &&
      offer.discountDetails.customerTiers.length > 0
    ) {
      const user = await User.findById(customerId);
      if (!user) {
        return { isEligible: false, reason: "Customer not found" };
      }

      // For now, we'll assume all customers are "regular" tier
      // This can be enhanced with actual customer tier logic
      const customerTier = user.tier || "regular";
      if (!offer.discountDetails.customerTiers.includes(customerTier)) {
        return {
          isEligible: false,
          reason: "Customer tier not eligible for this offer",
        };
      }
    }

    // Check first-time customer restriction
    if (offer.offerType === "firstTime") {
      const orderCount = await Order.countDocuments({ userId: customerId });
      if (orderCount > 0) {
        return {
          isEligible: false,
          reason: "Offer only available for first-time customers",
        };
      }
    }

    // Check per-customer usage limit
    if (offer.usageRules?.perCustomerLimit > 0) {
      const customerUsageCount = await Order.countDocuments({
        userId: customerId,
        appliedOffers: { $elemMatch: { offerId: offer._id } },
      });

      if (customerUsageCount >= offer.usageRules.perCustomerLimit) {
        return {
          isEligible: false,
          reason: "Customer usage limit reached for this offer",
        };
      }
    }

    return { isEligible: true };
  } catch (error) {
    console.error("Error checking customer eligibility:", error);
    return {
      isEligible: false,
      reason: "Error validating customer eligibility",
    };
  }
};

// Check offer type specific requirements
const checkOfferTypeRequirements = async (offer, orderData) => {
  const { items } = orderData;

  switch (offer.offerType) {
    case "multiDishType":
      const itemCategories = new Set();
      items.forEach((item) => {
        if (item.category) itemCategories.add(item.category.toString());
      });

      if (itemCategories.size < offer.discountDetails.minimumCategoriesCount) {
        return {
          isValid: false,
          reason: `Order must contain items from at least ${offer.discountDetails.minimumCategoriesCount} different categories`,
        };
      }
      break;

    case "BOGO":
    case "quantityDiscount":
      if (offer.applicableDishes && offer.applicableDishes.length > 0) {
        const applicableItems = items.filter((item) =>
          offer.applicableDishes.includes(
            item.dishId?.toString() || item.dishId
          )
        );

        const totalQuantity = applicableItems.reduce(
          (sum, item) => sum + item.quantity,
          0
        );
        if (totalQuantity < offer.discountDetails.buyQuantity) {
          return {
            isValid: false,
            reason: `Need to order at least ${offer.discountDetails.buyQuantity} of the applicable items`,
          };
        }
      }
      break;

    case "combo":
      // Check if all combo items are in the order
      if (
        offer.discountDetails.comboItems &&
        offer.discountDetails.comboItems.length > 0
      ) {
        const orderDishIds = items.map(
          (item) => item.dishId?.toString() || item.dishId
        );
        const missingItems = offer.discountDetails.comboItems.filter(
          (comboItem) => !orderDishIds.includes(comboItem.dishId.toString())
        );

        if (missingItems.length > 0) {
          return {
            isValid: false,
            reason: "All combo items must be in the order",
          };
        }
      }
      break;
  }

  return { isValid: true };
};

// Calculate estimated savings
const calculateEstimatedSavings = async (offer, orderData) => {
  const { orderAmount, items } = orderData;
  let estimatedSavings = 0;

  switch (offer.offerType) {
    case "discount":
    case "minimumAmount":
    case "dayOfWeek":
    case "dateRange":
    case "customerTier":
    case "firstTime":
    case "timeBasedSpecial":
      if (offer.discountDetails.discountType === "percentage") {
        estimatedSavings =
          (orderAmount * offer.discountDetails.discountValue) / 100;
        if (offer.discountDetails.maxDiscount) {
          estimatedSavings = Math.min(
            estimatedSavings,
            offer.discountDetails.maxDiscount
          );
        }
      } else {
        estimatedSavings = offer.discountDetails.discountValue;
      }
      break;

    case "BOGO":
      const applicableItems = items.filter(
        (item) =>
          !offer.applicableDishes?.length ||
          offer.applicableDishes.includes(item.dishId?.toString())
      );

      const { buyQuantity, getQuantity } = offer.discountDetails;

      // Special case: If customer has fewer items than buyQuantity but offer is Buy 1 Get 1
      if (applicableItems.length < buyQuantity) {
        if (
          buyQuantity === 1 &&
          getQuantity === 1 &&
          applicableItems.length === 1
        ) {
          // For Buy 1 Get 1 offers, give 50% discount on single item
          estimatedSavings = applicableItems[0].price * 0.5;
        }
      } else if (applicableItems.length >= buyQuantity) {
        // Sort by price and get the cheapest items for free
        const sortedItems = applicableItems.sort((a, b) => a.price - b.price);
        const freeItemsCount =
          Math.floor(applicableItems.length / buyQuantity) * getQuantity;

        for (let i = 0; i < Math.min(freeItemsCount, sortedItems.length); i++) {
          estimatedSavings += sortedItems[i].price;
        }
      }
      break;

    case "freeItem":
      if (offer.discountDetails.freeItemId?.price) {
        estimatedSavings =
          offer.discountDetails.freeItemId.price *
          (offer.discountDetails.freeItemQuantity || 1);
      }
      break;

    case "combo":
      if (offer.discountDetails.comboPrice) {
        const comboItemsTotal =
          offer.discountDetails.comboItems?.reduce((total, comboItem) => {
            return total + (comboItem.dishId?.price || 0) * comboItem.quantity;
          }, 0) || 0;

        estimatedSavings = Math.max(
          0,
          comboItemsTotal - offer.discountDetails.comboPrice
        );
      }
      break;
  }

  return Math.round(estimatedSavings * 100) / 100;
};

// Apply a specific offer to an order
export const applyOfferToOrder = async (offer, orderData) => {
  try {
    const { items, totalAmount, outletId, customerId } = orderData;

    // Validate offer applicability first
    const applicabilityCheck = await checkDetailedOfferApplicability(offer, {
      orderAmount: totalAmount,
      customerId,
      items,
      outletId,
    });

    if (!applicabilityCheck.isApplicable) {
      return {
        success: false,
        error: applicabilityCheck.reason,
        discount: 0,
      };
    }

    let discount = 0;
    let freeItems = [];

    switch (offer.offerType) {
      case "discount":
      case "minimumAmount":
      case "dayOfWeek":
      case "dateRange":
      case "customerTier":
      case "firstTime":
      case "timeBasedSpecial":
        discount = await calculateDiscountAmount(offer, totalAmount);
        break;

      case "BOGO":
        const bogoResult = await applyBOGOOffer(offer, items);
        discount = bogoResult.discount;
        freeItems = bogoResult.freeItems;
        break;

      case "quantityDiscount":
        discount = await applyQuantityDiscount(offer, items);
        break;

      case "freeItem":
        const freeItemResult = await applyFreeItemOffer(offer, items);
        freeItems = freeItemResult.freeItems;
        discount = freeItemResult.discount;
        break;

      case "combo":
        discount = await applyComboOffer(offer, items);
        break;

      case "multiDishType":
        discount = await calculateDiscountAmount(offer, totalAmount);
        break;

      default:
        return {
          success: false,
          error: "Unsupported offer type",
          discount: 0,
        };
    }

    return {
      success: true,
      discount: Math.round(discount * 100) / 100,
      freeItems,
      offerDetails: {
        id: offer._id,
        name: offer.name,
        type: offer.offerType,
      },
    };
  } catch (error) {
    console.error("Error applying offer to order:", error);
    return {
      success: false,
      error: error.message,
      discount: 0,
    };
  }
};

// Calculate discount amount for percentage/fixed offers
const calculateDiscountAmount = async (offer, totalAmount) => {
  if (offer.discountDetails.discountType === "percentage") {
    let discount = (totalAmount * offer.discountDetails.discountValue) / 100;
    if (offer.discountDetails.maxDiscount) {
      discount = Math.min(discount, offer.discountDetails.maxDiscount);
    }
    return discount;
  } else {
    return Math.min(offer.discountDetails.discountValue, totalAmount);
  }
};

// Apply BOGO offer
const applyBOGOOffer = async (offer, items) => {
  const applicableItems = items.filter(
    (item) =>
      !offer.applicableDishes?.length ||
      offer.applicableDishes.includes(item.dishId?.toString())
  );

  const { buyQuantity, getQuantity } = offer.discountDetails;

  // Special case: If customer has fewer items than buyQuantity but offer is Buy 1 Get 1
  // Give them proportional discount (e.g., 50% off for 1 item in Buy 1 Get 1)
  if (applicableItems.length < buyQuantity) {
    if (
      buyQuantity === 1 &&
      getQuantity === 1 &&
      applicableItems.length === 1
    ) {
      // For Buy 1 Get 1 offers, give 50% discount on single item
      const discount = applicableItems[0].price * 0.5;
      return { discount, freeItems: [] };
    }
    return { discount: 0, freeItems: [] };
  }

  // Sort by price (cheapest first for free items)
  const sortedItems = applicableItems.sort((a, b) => a.price - b.price);
  const freeItemsCount =
    Math.floor(applicableItems.length / buyQuantity) * getQuantity;

  let discount = 0;
  const freeItems = [];

  for (let i = 0; i < Math.min(freeItemsCount, sortedItems.length); i++) {
    discount += sortedItems[i].price;
    freeItems.push({
      dishId: sortedItems[i].dishId,
      dishName: sortedItems[i].dishName || sortedItems[i].name,
      quantity: 1,
      price: 0, // Free item
      originalPrice: sortedItems[i].price,
      isFreeItem: true,
      fromOffer: offer._id,
    });
  }

  return { discount, freeItems };
};

// Apply quantity discount
const applyQuantityDiscount = async (offer, items) => {
  const applicableItems = items.filter(
    (item) =>
      !offer.applicableDishes?.length ||
      offer.applicableDishes.includes(item.dishId?.toString())
  );

  const totalQuantity = applicableItems.reduce(
    (sum, item) => sum + item.quantity,
    0
  );

  if (totalQuantity < offer.discountDetails.buyQuantity) {
    return 0;
  }

  const applicableAmount = applicableItems.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );

  if (offer.discountDetails.discountType === "percentage") {
    let discount =
      (applicableAmount * offer.discountDetails.discountValue) / 100;
    if (offer.discountDetails.maxDiscount) {
      discount = Math.min(discount, offer.discountDetails.maxDiscount);
    }
    return discount;
  } else {
    return Math.min(offer.discountDetails.discountValue, applicableAmount);
  }
};

// Apply free item offer
const applyFreeItemOffer = async (offer, items) => {
  if (!offer.discountDetails.freeItemId) {
    return { discount: 0, freeItems: [] };
  }

  const freeItem = await Dish.findById(offer.discountDetails.freeItemId);
  if (!freeItem) {
    return { discount: 0, freeItems: [] };
  }

  const quantity = offer.discountDetails.freeItemQuantity || 1;
  const discount = freeItem.price * quantity;

  const freeItems = [
    {
      dishId: freeItem._id,
      dishName: freeItem.name,
      quantity,
      price: 0,
      originalPrice: freeItem.price,
      isFreeItem: true,
      fromOffer: offer._id,
    },
  ];

  return { discount, freeItems };
};

// Apply combo offer
const applyComboOffer = async (offer, items) => {
  if (!offer.discountDetails.comboItems || !offer.discountDetails.comboPrice) {
    return 0;
  }

  // Calculate original combo price
  let originalComboPrice = 0;
  const orderDishIds = items.map(
    (item) => item.dishId?.toString() || item.dishId
  );

  for (const comboItem of offer.discountDetails.comboItems) {
    if (orderDishIds.includes(comboItem.dishId.toString())) {
      const orderItem = items.find(
        (item) =>
          (item.dishId?.toString() || item.dishId) ===
          comboItem.dishId.toString()
      );
      if (orderItem) {
        originalComboPrice += orderItem.price * comboItem.quantity;
      }
    }
  }

  return Math.max(0, originalComboPrice - offer.discountDetails.comboPrice);
};

// Update offer usage analytics
const updateOfferUsage = async (offerId, discountAmount, customerId) => {
  try {
    const updateData = {
      $inc: {
        "usageRules.usedCount": 1,
        "analytics.totalUsage": 1,
        "analytics.totalSavings": discountAmount,
      },
    };

    if (customerId) {
      updateData.$addToSet = {
        "analytics.uniqueCustomers": customerId,
      };
    }

    await Offer.findByIdAndUpdate(offerId, updateData);
  } catch (error) {
    console.error("Error updating offer usage:", error);
  }
};

// Validate offer stacking rules
export const validateOfferStacking = async (offers, orderData) => {
  // Sort offers by priority
  const sortedOffers = offers.sort(
    (a, b) =>
      (b.stackingRules?.priority || 0) - (a.stackingRules?.priority || 0)
  );

  const applicableOffers = [];
  const appliedOfferTypes = new Set();

  for (const offer of sortedOffers) {
    // Check if this offer can stack with already applied offers
    if (
      applicableOffers.length > 0 &&
      !offer.stackingRules?.canStackWithOthers
    ) {
      continue; // This offer cannot stack with others
    }

    // Check if any already applied offer prevents stacking
    const hasConflict = applicableOffers.some(
      (appliedOffer) =>
        !appliedOffer.stackingRules?.canStackWithOthers ||
        (appliedOffer.stackingRules?.stackableOfferTypes &&
          !appliedOffer.stackingRules.stackableOfferTypes.includes(
            offer.offerType
          ))
    );

    if (hasConflict) {
      continue;
    }

    // Check if this offer type is allowed to stack with current offers
    if (offer.stackingRules?.stackableOfferTypes) {
      const hasIncompatibleType = [...appliedOfferTypes].some(
        (type) => !offer.stackingRules.stackableOfferTypes.includes(type)
      );

      if (hasIncompatibleType) {
        continue;
      }
    }

    applicableOffers.push(offer);
    appliedOfferTypes.add(offer.offerType);
  }

  return applicableOffers;
};
