import { pipeline } from "@xenova/transformers";
import DishEmbedding from "../models/DishEmbedding.js";
import Dish from "../models/Dish.js";

// Initialize the embedding pipeline (using open-source Sentence Transformers)
let embeddingPipeline = null;

/**
 * Initialize the embedding pipeline
 * @returns {Promise<Object>} - The embedding pipeline
 */
const initializeEmbeddingPipeline = async () => {
  if (!embeddingPipeline) {
    try {
      console.log("Initializing embedding pipeline...");
      // Using a lightweight, fast model for embeddings
      embeddingPipeline = await pipeline(
        "feature-extraction",
        "Xenova/all-MiniLM-L6-v2",
        {
          quantized: true, // Use quantized model for better performance
          progress_callback: null, // Disable progress logging
        }
      );
      console.log("Embedding pipeline initialized successfully");
    } catch (error) {
      console.error("Error initializing embedding pipeline:", error);
      throw error;
    }
  }
  return embeddingPipeline;
};

/**
 * Generate embeddings for a text using open-source Sentence Transformers
 * @param {string} text - The text to generate embeddings for
 * @returns {Promise<Array<number>>} - The embedding vector
 */
export const generateEmbedding = async (text) => {
  try {
    const pipeline = await initializeEmbeddingPipeline();

    // Generate embeddings
    const output = await pipeline(text, {
      pooling: "mean",
      normalize: true,
    });

    // Convert tensor to array
    const embeddings = Array.from(output.data);

    return embeddings;
  } catch (error) {
    console.error("Error generating embedding:", error);
    // Fallback: return a zero vector if embedding fails
    return new Array(384).fill(0); // MiniLM-L6-v2 produces 384-dimensional vectors
  }
};

/**
 * Create or update embeddings for a dish
 * @param {Object} dish - The dish object
 * @returns {Promise<Object>} - The created or updated embedding
 */
export const createOrUpdateDishEmbedding = async (dish) => {
  try {
    // Create text representation of the dish for embedding
    const dishText = `${dish.name} ${dish.description || ""} ${
      dish.cuisine || ""
    } ${dish.tags?.join(" ") || ""}`;

    // Generate embedding
    const embedding = await generateEmbedding(dishText);

    // Check if embedding already exists
    let dishEmbedding = await DishEmbedding.findOne({ dishId: dish._id });

    if (dishEmbedding) {
      // Update existing embedding
      dishEmbedding.embedding = embedding;
      dishEmbedding.metadata = {
        name: dish.name,
        description: dish.description,
        category: dish.category?.name || "",
        price: dish.price,
        tags: dish.tags || [],
        cuisine: dish.cuisine || "",
      };
      dishEmbedding.updatedAt = new Date();
      await dishEmbedding.save();
    } else {
      // Create new embedding
      dishEmbedding = new DishEmbedding({
        dishId: dish._id,
        foodChainId: dish.foodChain,
        outletId: dish.outlets[0], // Assuming the first outlet
        embedding: embedding,
        metadata: {
          name: dish.name,
          description: dish.description,
          category: dish.category?.name || "",
          price: dish.price,
          tags: dish.tags || [],
          cuisine: dish.cuisine || "",
        },
      });
      await dishEmbedding.save();
    }

    return dishEmbedding;
  } catch (error) {
    console.error("Error creating/updating dish embedding:", error);
    throw error;
  }
};

/**
 * Find similar dishes using vector similarity search with fallback
 * @param {string} query - The search query
 * @param {string} foodChainId - The food chain ID
 * @param {string} outletId - The outlet ID
 * @param {number} limit - Maximum number of results to return
 * @returns {Promise<Array<Object>>} - Array of similar dishes
 */
export const findSimilarDishes = async (
  query,
  foodChainId,
  outletId,
  limit = 5
) => {
  try {
    // First, try to use our enhanced menu search service as primary method
    const { searchDishes } = await import("./menu-search-service.js");

    // Get available dishes for this outlet
    const availableDishes = await Dish.find({
      foodChain: foodChainId,
      isAvailable: true,
      outlets: outletId,
    }).populate("category", "name");

    // Use enhanced search as primary method
    const searchResults = await searchDishes(query, availableDishes, {
      limit,
      threshold: 0.1,
      filters: { availableOnly: true },
    });

    if (searchResults.length > 0) {
      // Convert search results to vector search format for compatibility
      return searchResults.map((dish) => ({
        dish: dish,
        similarity: dish.relevanceScore || 0.5,
        metadata: {
          name: dish.name,
          description: dish.description,
          category: dish.category?.name || "",
          price: dish.price,
          tags: dish.tags || [],
          cuisine: dish.cuisine || "",
        },
      }));
    }

    // Fallback to vector search if enhanced search returns no results
    return await findSimilarDishesVector(query, foodChainId, outletId, limit);
  } catch (error) {
    console.error("Error finding similar dishes:", error);
    // Final fallback: return empty array
    return [];
  }
};

/**
 * Find similar dishes using pure vector similarity search (fallback method)
 * @param {string} query - The search query
 * @param {string} foodChainId - The food chain ID
 * @param {string} outletId - The outlet ID
 * @param {number} limit - Maximum number of results to return
 * @returns {Promise<Array<Object>>} - Array of similar dishes
 */
export const findSimilarDishesVector = async (
  query,
  foodChainId,
  outletId,
  limit = 5
) => {
  try {
    // Generate embedding for the query
    const queryEmbedding = await generateEmbedding(query);

    // Find dishes with similar embeddings
    const dishEmbeddings = await DishEmbedding.find({
      foodChainId,
      outletId,
    }).populate("dishId");

    if (dishEmbeddings.length === 0) {
      console.log("No dish embeddings found, returning empty results");
      return [];
    }

    // Calculate cosine similarity
    const results = dishEmbeddings.map((embedding) => {
      const similarity = calculateCosineSimilarity(
        queryEmbedding,
        embedding.embedding
      );
      return {
        dish: embedding.dishId,
        similarity,
        metadata: embedding.metadata,
      };
    });

    // Sort by similarity (highest first) and limit results
    return results.sort((a, b) => b.similarity - a.similarity).slice(0, limit);
  } catch (error) {
    console.error("Error in vector similarity search:", error);
    return [];
  }
};

/**
 * Calculate cosine similarity between two vectors
 * @param {Array<number>} vec1 - First vector
 * @param {Array<number>} vec2 - Second vector
 * @returns {number} - Cosine similarity (between -1 and 1)
 */
const calculateCosineSimilarity = (vec1, vec2) => {
  if (vec1.length !== vec2.length) {
    throw new Error("Vectors must have the same length");
  }

  let dotProduct = 0;
  let mag1 = 0;
  let mag2 = 0;

  for (let i = 0; i < vec1.length; i++) {
    dotProduct += vec1[i] * vec2[i];
    mag1 += vec1[i] * vec1[i];
    mag2 += vec2[i] * vec2[i];
  }

  mag1 = Math.sqrt(mag1);
  mag2 = Math.sqrt(mag2);

  if (mag1 === 0 || mag2 === 0) {
    return 0;
  }

  return dotProduct / (mag1 * mag2);
};

/**
 * Generate embeddings for all dishes in the database
 * @returns {Promise<number>} - Number of dishes processed
 */
export const generateAllDishEmbeddings = async () => {
  try {
    const dishes = await Dish.find().populate("category", "name");
    let count = 0;

    for (const dish of dishes) {
      await createOrUpdateDishEmbedding(dish);
      count++;
    }

    return count;
  } catch (error) {
    console.error("Error generating all dish embeddings:", error);
    throw error;
  }
};
