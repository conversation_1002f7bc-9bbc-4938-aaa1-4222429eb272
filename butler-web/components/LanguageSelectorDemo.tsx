import React from "react";
import LanguageSelector from "./LanguageSelector";
import {
  useLanguagePreference,
  getLanguageInfo,
} from "../hooks/useLanguagePreference";

const LanguageSelectorDemo: React.FC = () => {
  const { language, setLanguage } = useLanguagePreference();
  const languageInfo = getLanguageInfo(language);

  // Sample responses in different languages
  const sampleResponses = {
    en: {
      aiMessage:
        "I recommend the Margherita Pizza for you. It's our specialty!",
      faqSuggestions: [
        "What's your specialty?",
        "Show me the menu",
        "Any vegetarian options?",
      ],
    },
    hi: {
      aiMessage:
        "मैं आपके लिए मार्गेरिटा पिज्जा सुझाता हूँ। यह हमारी विशेषता है!",
      faqSuggestions: [
        "आपकी विशेषता क्या है?",
        "मुझे मेन्यू दिखाएं",
        "कोई शाकाहारी विकल्प?",
      ],
    },
    "hi-en": {
      aiMessage:
        "Main aapke liye Margherita Pizza suggest karta hun. Yeh hamari specialty hai!",
      faqSuggestions: [
        "Aapki specialty kya hai?",
        "Menu dikhao",
        "Koi veg options?",
      ],
    },
    ta: {
      aiMessage:
        "நான் உங்களுக்கு மார்கெரிட்டா பிஸ்ஸா பரிந்துரைக்கிறேன். இது எங்கள் சிறப்பு!",
      faqSuggestions: [
        "உங்கள் சிறப்பு என்ன?",
        "மெனுவைக் காட்டுங்கள்",
        "சைவ விருப்பங்கள்?",
      ],
    },
    te: {
      aiMessage:
        "నేను మీకు మార్గెరిటా పిజ్జా సిఫార్సు చేస్తున్నాను. ఇది మా ప్రత్యేకత!",
      faqSuggestions: [
        "మీ ప్రత్యేకత ఏమిటి?",
        "మెనూ చూపించండి",
        "శాకాహార ఎంపికలు?",
      ],
    },
    bn: {
      aiMessage:
        "আমি আপনার জন্য মার্গেরিটা পিজা সুপারিশ করি। এটি আমাদের বিশেষত্ব!",
      faqSuggestions: ["আপনার বিশেষত্ব কী?", "মেনু দেখান", "নিরামিষ বিকল্প?"],
    },
    mr: {
      aiMessage: "मी तुमच्यासाठी मार्गेरिटा पिझ्झा सुचवतो. ही आमची खासियत आहे!",
      faqSuggestions: [
        "तुमची खासियत काय आहे?",
        "मेनू दाखवा",
        "शाकाहारी पर्याय?",
      ],
    },
    gu: {
      aiMessage:
        "હું તમારા માટે માર્ગેરિટા પિઝા સૂચવું છું. આ અમારી વિશેષતા છે!",
      faqSuggestions: [
        "તમારી વિશેષતા શું છે?",
        "મેનૂ બતાવો",
        "શાકાહારી વિકલ્પો?",
      ],
    },
  };

  const currentResponse = sampleResponses[language] || sampleResponses.en;

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">
          🌍 Multi-Language Butler AI Demo
        </h2>

        <div className="grid md:grid-cols-2 gap-6">
          {/* Language Selector Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-700">
              Language Selection
            </h3>

            <div className="bg-gray-50 p-4 rounded-lg">
              <label className="block text-sm font-medium text-gray-600 mb-2">
                Choose your conversation language:
              </label>
              <LanguageSelector
                selectedLanguage={language}
                onLanguageChange={setLanguage}
              />
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">
                Current Selection:
              </h4>
              <div className="space-y-1 text-sm">
                <p>
                  <strong>Code:</strong> {language}
                </p>
                <p>
                  <strong>Display:</strong> {languageInfo.nativeName}
                </p>
                <p>
                  <strong>Full Name:</strong> {languageInfo.name}
                </p>
                <p>
                  <strong>Flag:</strong> {languageInfo.flag}
                </p>
              </div>
            </div>
          </div>

          {/* Sample Response Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-700">
              AI Response Preview
            </h3>

            <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-4 rounded-lg">
              <h4 className="font-medium mb-2">🤖 Butler AI Response:</h4>
              <p className="text-sm">{currentResponse.aiMessage}</p>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-700 mb-2">
                💬 FAQ Suggestions:
              </h4>
              <div className="space-y-2">
                {currentResponse.faqSuggestions.map((suggestion, index) => (
                  <div
                    key={index}
                    className="bg-white px-3 py-2 rounded border text-sm hover:bg-gray-100 cursor-pointer transition-colors"
                  >
                    {suggestion}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Technical Details */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-4">
          🔧 Technical Implementation
        </h3>

        <div className="grid md:grid-cols-3 gap-4">
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-medium text-green-800 mb-2">✅ Keywords</h4>
            <p className="text-sm text-green-700">
              Always in English for consistent search functionality
            </p>
            <code className="text-xs bg-green-100 px-2 py-1 rounded mt-2 block">
              [&quot;pizza&quot;, &quot;margherita&quot;, &quot;specialty&quot;]
            </code>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-800 mb-2">🗣️ AI Messages</h4>
            <p className="text-sm text-blue-700">
              Dynamic language based on user selection
            </p>
            <code className="text-xs bg-blue-100 px-2 py-1 rounded mt-2 block">
              Language: {language.toUpperCase()}
            </code>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-medium text-purple-800 mb-2">💾 Persistence</h4>
            <p className="text-sm text-purple-700">
              Saved in localStorage for future sessions
            </p>
            <code className="text-xs bg-purple-100 px-2 py-1 rounded mt-2 block">
              butler-conversation-language
            </code>
          </div>
        </div>
      </div>

      {/* Supported Languages */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-4">
          🇮🇳 Supported Languages
        </h3>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {[
            { code: "en", name: "English", short: "ENG", flag: "🇺🇸" },
            { code: "hi", name: "Hindi", short: "HI", flag: "🇮🇳" },
            { code: "hi-en", name: "Hinglish", short: "HIN-ENG", flag: "🇮🇳" },
            { code: "ta", name: "Tamil", short: "TA", flag: "🇮🇳" },
            { code: "te", name: "Telugu", short: "TE", flag: "🇮🇳" },
            { code: "bn", name: "Bengali", short: "BN", flag: "🇮🇳" },
            { code: "mr", name: "Marathi", short: "MR", flag: "🇮🇳" },
            { code: "gu", name: "Gujarati", short: "GU", flag: "🇮🇳" },
          ].map((lang) => (
            <div
              key={lang.code}
              className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                language === lang.code
                  ? "border-indigo-500 bg-indigo-50"
                  : "border-gray-200 hover:border-gray-300"
              }`}
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              onClick={() => setLanguage(lang.code as any)}
            >
              <div className="text-center">
                <div className="text-2xl mb-1">{lang.flag}</div>
                <div className="font-medium text-sm">{lang.short}</div>
                <div className="text-xs text-gray-500">{lang.name}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Usage Instructions */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-4">
          📋 How to Use
        </h3>

        <div className="space-y-3 text-sm">
          <div className="flex items-start space-x-3">
            <span className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
              1
            </span>
            <p>
              Select your preferred language from the dropdown in the chat
              header
            </p>
          </div>
          <div className="flex items-start space-x-3">
            <span className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
              2
            </span>
            <p>Start chatting with Butler AI in your selected language</p>
          </div>
          <div className="flex items-start space-x-3">
            <span className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
              3
            </span>
            <p>
              AI will respond in your chosen language while keeping keywords in
              English for search
            </p>
          </div>
          <div className="flex items-start space-x-3">
            <span className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
              4
            </span>
            <p>
              Your language preference is automatically saved for future
              conversations
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LanguageSelectorDemo;
