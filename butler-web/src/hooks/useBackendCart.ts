"use client";
import { Dish, Offer } from "@/app/type";
import { useEffect, useState, useCallback } from "react";
import { useSearchParams } from "next/navigation";
import {
  getCart,
  addToCart as addToCartAP<PERSON>,
  removeFromCart as removeFrom<PERSON><PERSON><PERSON><PERSON>,
  updateCartItemQuantity as updateCartItemQuantityAPI,
  clearCart as clearCartAPI,
  // syncCart, // Unused for now
  migrateLocalStorageCart,
  isAuthenticated,
} from "@/server/cart";
import { toast } from "sonner";
import useCartSocket from "./useCartSocket";

interface CartData {
  items: Dish[];
  appliedOffers: Offer[];
  subtotal: number;
  totalOfferDiscount: number;
  totalCouponDiscount: number;
  totalDiscount: number;
  finalTotal: number;
  taxAmount: number;
  deliveryFee: number;
  packagingFee: number;
  updatedAt: string;
}

const useBackendCart = () => {
  const [cart, setCart] = useState<Dish[]>([]);
  const [appliedOffers, setAppliedOffers] = useState<Offer[]>([]);
  const [cartTotals, setCartTotals] = useState({
    subtotal: 0,
    totalOfferDiscount: 0,
    totalCouponDiscount: 0,
    totalDiscount: 0,
    finalTotal: 0,
    taxAmount: 0,
    deliveryFee: 0,
    packagingFee: 0,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const params = useSearchParams();

  // Get user ID for socket connection
  const getUserId = useCallback((): string | null => {
    if (typeof window === "undefined") return null;
    const token = localStorage.getItem("user-token");
    if (!token) return null;

    try {
      const payload = JSON.parse(atob(token.split(".")[1]));
      return payload.userId || payload.id || null;
    } catch {
      return null;
    }
  }, []);

  // Get foodChainId and outletId from URL params or localStorage
  const getFoodChainId = useCallback((): string => {
    const urlChainId = params?.get("chainId");
    if (urlChainId) return urlChainId;
    return localStorage.getItem("chainId") || "";
  }, [params]);

  const getOutletId = useCallback((): string => {
    const urlOutletId = params?.get("outletId");
    if (urlOutletId) return urlOutletId;
    return localStorage.getItem("outletId") || "";
  }, [params]);

  // Update cart state from backend response
  const updateCartState = useCallback((cartData: CartData) => {
    // Transform backend cart items to frontend format
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const transformedItems = (cartData.items || []).map((item: any) => {
      // Handle both populated and non-populated dishId
      const dishData =
        item.dishId && typeof item.dishId === "object" ? item.dishId : item;

      return {
        _id: dishData._id || item.dishId,
        name: dishData.name || item.dishName,
        price: dishData.price || item.price,
        quantity: item.quantity,
        image: dishData.image,
        isVeg: dishData.isVeg,
        category: dishData.category,
        description: dishData.description,
        // Add required Dish properties with defaults
        foodChain: dishData.foodChain || "",
        outlets: dishData.outlets || [],
        isAvailable:
          dishData.isAvailable !== undefined ? dishData.isAvailable : true,
      } as Dish;
    });

    setCart(transformedItems);
    setAppliedOffers(cartData.appliedOffers || []);
    setCartTotals({
      subtotal: cartData.subtotal || 0,
      totalOfferDiscount: cartData.totalOfferDiscount || 0,
      totalCouponDiscount: cartData.totalCouponDiscount || 0,
      totalDiscount: cartData.totalDiscount || 0,
      finalTotal: cartData.finalTotal || 0,
      taxAmount: cartData.taxAmount || 0,
      deliveryFee: cartData.deliveryFee || 0,
      packagingFee: cartData.packagingFee || 0,
    });
  }, []);

  // Load cart from backend
  const loadCart = useCallback(
    async (silent = false) => {
      if (!isAuthenticated()) {
        return;
      }

      const foodChainId = getFoodChainId();
      const outletId = getOutletId();

      if (!foodChainId || !outletId) {
        return;
      }

      if (!silent) {
        setLoading(true);
      }
      setError(null);

      try {
        // First try to migrate localStorage cart if it exists
        const result = await migrateLocalStorageCart(foodChainId, outletId);

        if (result.success && result.data) {
          updateCartState(result.data);
          console.log("🛒 Cart loaded successfully:", result.data);
        } else {
          // If migration fails, just get the backend cart
          const cartResult = await getCart(foodChainId, outletId);
          if (cartResult.success && cartResult.data) {
            updateCartState(cartResult.data);
            console.log("🛒 Cart loaded successfully:", cartResult.data);
          } else {
            setError(cartResult.message || "Failed to load cart");
          }
        }
      } catch (error) {
        console.error("Error loading cart:", error);
        setError("Failed to load cart");
      } finally {
        if (!silent) {
          setLoading(false);
        }
      }
    },
    [updateCartState, getFoodChainId, getOutletId]
  );

  // Load cart on component mount and when params change
  useEffect(() => {
    loadCart();
  }, [loadCart]);

  // Add item to cart
  const addToCart = useCallback(
    async (dish: Dish, quantity: number = 1) => {
      if (!isAuthenticated()) {
        toast.error("Please login to add items to cart");
        return false;
      }

      const foodChainId = getFoodChainId();
      const outletId = getOutletId();

      if (!foodChainId || !outletId) {
        toast.error("Food chain and outlet information is required");
        return false;
      }

      setLoading(true);
      setError(null);

      try {
        const result = await addToCartAPI(
          dish._id!,
          quantity,
          foodChainId,
          outletId
        );

        if (result.success && result.data) {
          updateCartState(result.data);
          toast.success("Item added to cart");
          return true;
        } else {
          setError(result.message || "Failed to add item to cart");
          toast.error(result.message || "Failed to add item to cart");
          return false;
        }
      } catch (error) {
        console.error("Error adding item to cart:", error);
        setError("Failed to add item to cart");
        toast.error("Failed to add item to cart");
        return false;
      } finally {
        setLoading(false);
      }
    },
    [updateCartState, getFoodChainId, getOutletId]
  );

  // Remove item from cart
  const removeFromCart = useCallback(
    async (dishId: string) => {
      if (!isAuthenticated()) {
        toast.error("Please login to modify cart");
        return false;
      }

      const foodChainId = getFoodChainId();
      const outletId = getOutletId();

      if (!foodChainId || !outletId) {
        toast.error("Food chain and outlet information is required");
        return false;
      }

      setLoading(true);
      setError(null);

      try {
        const result = await removeFromCartAPI(dishId, foodChainId, outletId);

        if (result.success && result.data) {
          updateCartState(result.data);
          toast.success("Item removed from cart");
          return true;
        } else {
          setError(result.message || "Failed to remove item from cart");
          toast.error(result.message || "Failed to remove item from cart");
          return false;
        }
      } catch (error) {
        console.error("Error removing item from cart:", error);
        setError("Failed to remove item from cart");
        toast.error("Failed to remove item from cart");
        return false;
      } finally {
        setLoading(false);
      }
    },
    [updateCartState, getFoodChainId, getOutletId]
  );

  // Update item quantity in cart
  const updateItemQuantity = useCallback(
    async (dishId: string, quantity: number) => {
      if (!isAuthenticated()) {
        toast.error("Please login to modify cart");
        return false;
      }

      const foodChainId = getFoodChainId();
      const outletId = getOutletId();

      if (!foodChainId || !outletId) {
        toast.error("Food chain and outlet information is required");
        return false;
      }

      console.log("Updating cart item quantity:", {
        dishId,
        quantity,
        foodChainId,
        outletId,
      });
      setLoading(true);
      setError(null);

      try {
        const result = await updateCartItemQuantityAPI(
          dishId,
          quantity,
          foodChainId,
          outletId
        );
        console.log("Update cart item quantity result:", result);

        if (result.success && result.data) {
          updateCartState(result.data);
          toast.success("Cart updated successfully");
          return true;
        } else {
          setError(result.message || "Failed to update item quantity");
          toast.error(result.message || "Failed to update item quantity");
          return false;
        }
      } catch (error) {
        console.error("Error updating item quantity:", error);
        setError("Failed to update item quantity");
        toast.error("Failed to update item quantity");
        return false;
      } finally {
        setLoading(false);
      }
    },
    [updateCartState, getFoodChainId, getOutletId]
  );

  // Clear cart
  const clearCart = useCallback(async () => {
    if (!isAuthenticated()) {
      toast.error("Please login to clear cart");
      return false;
    }

    const foodChainId = getFoodChainId();
    const outletId = getOutletId();

    if (!foodChainId || !outletId) {
      toast.error("Food chain and outlet information is required");
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await clearCartAPI(foodChainId, outletId);

      if (result.success) {
        setCart([]);
        setAppliedOffers([]);
        setCartTotals({
          subtotal: 0,
          totalOfferDiscount: 0,
          totalCouponDiscount: 0,
          totalDiscount: 0,
          finalTotal: 0,
          taxAmount: 0,
          deliveryFee: 0,
          packagingFee: 0,
        });
        toast.success("Cart cleared");
        return true;
      } else {
        setError(result.message || "Failed to clear cart");
        toast.error(result.message || "Failed to clear cart");
        return false;
      }
    } catch (error) {
      console.error("Error clearing cart:", error);
      setError("Failed to clear cart");
      toast.error("Failed to clear cart");
      return false;
    } finally {
      setLoading(false);
    }
  }, [getFoodChainId, getOutletId]);

  // Refresh cart data
  const refreshCart = useCallback(
    async (silent = false) => {
      console.log("🔄 Refreshing cart...");
      await loadCart(silent);
    },
    [loadCart]
  );

  // Auto-refresh cart every 30 seconds when user is active
  useEffect(() => {
    const interval = setInterval(() => {
      if (document.visibilityState === "visible" && isAuthenticated()) {
        refreshCart(true); // Silent refresh
      }
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [refreshCart]);

  // Refresh cart when page becomes visible (user switches back to tab)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && isAuthenticated()) {
        refreshCart(true);
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () =>
      document.removeEventListener("visibilitychange", handleVisibilityChange);
  }, [refreshCart]);

  // Cart socket integration for real-time updates
  const { isConnected: socketConnected } = useCartSocket(
    getUserId(),
    getFoodChainId(),
    getOutletId(),
    (cartData) => {
      console.log("🔄 Real-time cart update received:", cartData);
      updateCartState(cartData);
    },
    (operationData) => {
      console.log("🤖 AI cart operation received:", operationData);
      if (operationData.success) {
        toast.success(operationData.message);
      } else {
        toast.error(operationData.message);
      }
    }
  );

  console.log(
    "🔌 Cart socket status:",
    socketConnected ? "Connected" : "Disconnected"
  );

  // Get cart item count
  const getCartItemCount = useCallback(() => {
    return cart.reduce((total, item) => total + (item.quantity || 1), 0);
  }, [cart]);

  // Check if item is in cart
  const isItemInCart = useCallback(
    (dishId: string) => {
      return cart.some((item) => item._id === dishId);
    },
    [cart]
  );

  // Get item quantity in cart
  const getItemQuantity = useCallback(
    (dishId: string) => {
      const item = cart.find((item) => item._id === dishId);
      return item?.quantity || 0;
    },
    [cart]
  );

  return {
    cart,
    appliedOffers,
    cartTotals,
    loading,
    error,
    addToCart,
    removeFromCart,
    updateItemQuantity,
    clearCart,
    refreshCart,
    getCartItemCount,
    isItemInCart,
    getItemQuantity,
    // Legacy compatibility
    setCart: () => {
      console.warn(
        "setCart is deprecated when using backend cart. Use addToCart/removeFromCart instead."
      );
    },
    setAppliedOffers: () => {
      console.warn(
        "setAppliedOffers is deprecated when using backend cart. Offers are managed automatically."
      );
    },
  };
};

export default useBackendCart;
