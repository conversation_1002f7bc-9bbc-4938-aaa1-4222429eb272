import { NextRequest, NextResponse } from "next/server";

const BACKEND_URL =
  process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:3001";

export async function POST(request: NextRequest) {
  try {
    console.log("🎤 Frontend: Audio transcription request received");

    // Get the form data from the request
    const formData = await request.formData();

    // Forward the request to the backend
    const backendResponse = await fetch(`${BACKEND_URL}/api/transcribe-audio`, {
      method: "POST",
      body: formData,
      // Don't set Content-Type header, let fetch handle it for FormData
    });

    const result = await backendResponse.json();

    if (backendResponse.ok) {
      console.log("✅ Frontend: Transcription successful");
      return NextResponse.json(result);
    } else {
      console.error("❌ Frontend: Transcription failed:", result);
      return NextResponse.json(result, { status: backendResponse.status });
    }
  } catch (error) {
    console.error("Frontend transcription API error:", error);

    return NextResponse.json(
      {
        success: false,
        error: "Failed to process audio transcription",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Test endpoint
    const backendResponse = await fetch(
      `${BACKEND_URL}/api/transcribe-audio/test`
    );
    const result = await backendResponse.json();

    return NextResponse.json(result);
  } catch (error) {
    console.log(error);
    return NextResponse.json(
      {
        success: false,
        error: "Audio transcription service unavailable",
      },
      { status: 503 }
    );
  }
}
