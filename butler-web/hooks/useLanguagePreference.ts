import { useState, useEffect } from "react";

export type SupportedLanguage =
  | "en"
  | "hi"
  | "es"
  | "fr"
  | "de"
  | "it"
  | "pt"
  | "ru"
  | "ja"
  | "ko"
  | "zh"
  | "ar";

interface UseLanguagePreferenceReturn {
  language: SupportedLanguage;
  setLanguage: (language: SupportedLanguage) => void;
  isLoading: boolean;
}

const DEFAULT_LANGUAGE: SupportedLanguage = "en";
const STORAGE_KEY = "butler-conversation-language";

export const useLanguagePreference = (): UseLanguagePreferenceReturn => {
  const [language, setLanguageState] =
    useState<SupportedLanguage>(DEFAULT_LANGUAGE);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Load language preference from localStorage
    const loadLanguagePreference = () => {
      try {
        if (typeof window !== "undefined") {
          const stored = localStorage.getItem(STORAGE_KEY);
          if (
            stored &&
            ["en", "hi", "hi-en", "ta", "te", "bn", "mr", "gu"].includes(stored)
          ) {
            setLanguageState(stored as SupportedLanguage);
          }
        }
      } catch (error) {
        console.warn("Failed to load language preference:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadLanguagePreference();
  }, []);

  const setLanguage = (newLanguage: SupportedLanguage) => {
    setLanguageState(newLanguage);

    // Save to localStorage
    try {
      if (typeof window !== "undefined") {
        localStorage.setItem(STORAGE_KEY, newLanguage);
      }
    } catch (error) {
      console.warn("Failed to save language preference:", error);
    }
  };

  return {
    language,
    setLanguage,
    isLoading,
  };
};

// Helper function to get language display info
export const getLanguageInfo = (code: SupportedLanguage) => {
  const languageMap = {
    en: { name: "English", nativeName: "ENG", flag: "🇺🇸" },
    hi: { name: "Hindi", nativeName: "HI", flag: "🇮🇳" },
    "hi-en": { name: "Hinglish", nativeName: "HIN-ENG", flag: "🇮🇳" },
    ta: { name: "Tamil", nativeName: "TA", flag: "🇮🇳" },
    te: { name: "Telugu", nativeName: "TE", flag: "🇮🇳" },
    bn: { name: "Bengali", nativeName: "BN", flag: "🇮🇳" },
    mr: { name: "Marathi", nativeName: "MR", flag: "🇮🇳" },
    gu: { name: "Gujarati", nativeName: "GU", flag: "🇮🇳" },
  };

  return languageMap[code] || languageMap["en"];
};

// Helper function to format language for API calls
export const formatLanguageForAPI = (language: SupportedLanguage): string => {
  return language;
};

// Helper function to detect if browser language matches supported languages
export const detectBrowserLanguage = (): SupportedLanguage => {
  if (typeof window === "undefined") return DEFAULT_LANGUAGE;

  const browserLang = navigator.language.toLowerCase();

  // Map browser language codes to our supported languages
  if (browserLang.startsWith("hi")) return "hi";
  if (browserLang.startsWith("ta")) return "ta";
  if (browserLang.startsWith("te")) return "te";
  if (browserLang.startsWith("bn")) return "bn";
  if (browserLang.startsWith("mr")) return "mr";
  if (browserLang.startsWith("gu")) return "gu";

  return "en";
};
